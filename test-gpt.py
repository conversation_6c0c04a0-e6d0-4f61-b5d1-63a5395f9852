import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

# 1. 导入你的模型定义并加载训练好的权重
from models.resnet import ResNet18

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = ResNet18(num_classes=10).to(device)
model.load_state_dict(torch.load('./model-cifar-wideResNet/model-resnet18-epoch90.pt'))

model.eval()

# 2. 构造测试集 DataLoader
test_loader = DataLoader(
    datasets.CIFAR10('./data', train=False, download=True,
                     transform=transforms.ToTensor()),
    batch_size=128, shuffle=False,
    num_workers=1, pin_memory=True
)

# 3. 导入攻击方法
from torchattacks import FGSM, PGD, CW
from autoattack import AutoAttack

attacks = {
    'clean': None,
    'fgsm': FGSM(model, eps=8/255),
    'pgd': PGD(model, eps=8/255, alpha=2/255, steps=20),
    'cw' : CW(model, c=1, kappa=0, steps=1000),
    'aa' : AutoAttack(model, norm='Linf', eps=8/255)
}

# 4. 定义评估函数
def evaluate(model, loader, attack):
    correct = 0
    total = 0
    for x, y in loader:
        x, y = x.to(device), y.to(device)
        # 如果是 clean，直接预测；否则先生成对抗样本
        if attack is not None:
            x = attack(x, y)
        logits = model(x)
        pred = logits.argmax(dim=1)
        correct += pred.eq(y).sum().item()
        total += y.size(0)
    return 100. * correct / total

# 5. 逐个攻击评估
results = {}
for name, atk in attacks.items():
    acc = evaluate(model, test_loader, atk)
    results[name] = acc
    print(f"{name.upper():>5} Accuracy: {acc:.2f}%")

# 示例输出
# CLEAN Accuracy: 82.50%
#  FGSM Accuracy: 45.30%
#   PGD Accuracy: 50.10%
#    CW  Accuracy: 48.90%
#     AA Accuracy: 47.20%