from __future__ import print_function
import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import torch.optim as optim
from torchvision import datasets, transforms
# 添加混合精度训练需要的库
from torch.cuda.amp import autocast, GradScaler
import datetime
import math
# 导入梯度裁剪工具
from torch.nn.utils import clip_grad_norm_

from models.wideresnet import *
from models.resnet import *
from trades import trades_loss

parser = argparse.ArgumentParser(description='PyTorch FashionMNIST TRADES Adversarial Training')
parser.add_argument('--batch-size', type=int, default=128, metavar='N',
                    help='input batch size for training (default: 128)')
parser.add_argument('--test-batch-size', type=int, default=128, metavar='N',
                    help='input batch size for testing (default: 128)')
parser.add_argument('--epochs', type=int, default=100, metavar='N',
                    help='number of epochs to train')
parser.add_argument('--weight-decay', '--wd', default=5e-4,
                    type=float, metavar='W')
parser.add_argument('--lr', type=float, default=0.1, metavar='LR',
                    help='learning rate')
parser.add_argument('--momentum', type=float, default=0.9, metavar='M',
                    help='SGD momentum')
parser.add_argument('--no-cuda', action='store_true', default=False,
                    help='disables CUDA training')
parser.add_argument('--epsilon', type=float, default=0.031,
                    help='perturbation')
parser.add_argument('--num-steps', type=int, default=10,
                    help='perturb number of steps')
parser.add_argument('--step-size', type=float, default=0.007,
                    help='perturb step size')
parser.add_argument('--beta', type=float, default=6.0,
                    help='regularization, i.e., 1/lambda in TRADES')
parser.add_argument('--seed', type=int, default=1, metavar='S',
                    help='random seed (default: 1)')
parser.add_argument('--log-interval', type=int, default=100, metavar='N',
                    help='how many batches to wait before logging training status')
parser.add_argument('--model-dir', default='./checkpoints',
                    help='base directory of model for saving checkpoint')
parser.add_argument('--save-freq', '-s', default=10, type=int, metavar='N',
                    help='save frequency')
parser.add_argument('--model', default='wrn', choices=['wrn', 'resnet18', 'resnet50'],
                    help='model architecture')
# 添加混合精度训练的参数
parser.add_argument('--amp', action='store_true', default=False,
                    help='enable automatic mixed precision training')
parser.add_argument('--warmup-epochs', type=int, default=5, 
                    help='number of warmup epochs')
# 添加梯度裁剪参数
parser.add_argument('--clip-grad', type=float, default=1.0,
                    help='gradient clipping norm (default: 1.0)')

args = parser.parse_args()

# settings
model_dir = args.model_dir
if not os.path.exists(model_dir):
    os.makedirs(model_dir)
use_cuda = not args.no_cuda and torch.cuda.is_available()
torch.manual_seed(args.seed)
device = torch.device("cuda" if use_cuda else "cpu")
kwargs = {'num_workers': 1, 'pin_memory': True} if use_cuda else {}

# setup data loader
transform_train = transforms.Compose([
    transforms.ToTensor(),
])
transform_test = transforms.Compose([
    transforms.ToTensor(),
])
trainset = datasets.FashionMNIST(root='./data', train=True, download=True, transform=transform_train)
train_loader = torch.utils.data.DataLoader(trainset, batch_size=args.batch_size, shuffle=True, **kwargs)
testset = datasets.FashionMNIST(root='./data', train=False, download=True, transform=transform_test)
test_loader = torch.utils.data.DataLoader(testset, batch_size=args.test_batch_size, shuffle=False, **kwargs)


def train(args, model, device, train_loader, optimizer, epoch):
    model.train()
    # 如果启用混合精度训练，则创建GradScaler
    scaler = GradScaler() if args.amp else None
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()

        # 使用混合精度训练
        if args.amp:
            with autocast():
                # calculate robust loss
                loss = trades_loss(model=model,
                                x_natural=data,
                                y=target,
                                optimizer=optimizer,
                                step_size=args.step_size,
                                epsilon=args.epsilon,
                                perturb_steps=args.num_steps,
                                beta=args.beta)
            
            # 使用scaler来缩放梯度并更新参数
            scaler.scale(loss).backward()
            
            # 梯度裁剪（AMP版本）
            scaler.unscale_(optimizer)
            clip_grad_norm_(model.parameters(), args.clip_grad)
            
            scaler.step(optimizer)
            scaler.update()
        else:
            # 原始的训练流程
            loss = trades_loss(model=model,
                            x_natural=data,
                            y=target,
                            optimizer=optimizer,
                            step_size=args.step_size,
                            epsilon=args.epsilon,
                            perturb_steps=args.num_steps,
                            beta=args.beta)
            loss.backward()
            
            # 添加梯度裁剪
            clip_grad_norm_(model.parameters(), args.clip_grad)
            
            optimizer.step()

        # print progress
        if batch_idx % args.log_interval == 0:
            print('Train Epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(
                epoch, batch_idx * len(data), len(train_loader.dataset),
                       100. * batch_idx / len(train_loader), loss.item()))


def eval_train(model, device, train_loader):
    model.eval()
    train_loss = 0
    correct = 0
    with torch.no_grad():
        for data, target in train_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            train_loss += F.cross_entropy(output, target, size_average=False).item()
            pred = output.max(1, keepdim=True)[1]
            correct += pred.eq(target.view_as(pred)).sum().item()
    train_loss /= len(train_loader.dataset)
    print('Training: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)'.format(
        train_loss, correct, len(train_loader.dataset),
        100. * correct / len(train_loader.dataset)))
    training_accuracy = correct / len(train_loader.dataset)
    return train_loss, training_accuracy


def eval_test(model, device, test_loader):
    model.eval()
    test_loss = 0
    correct = 0
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            test_loss += F.cross_entropy(output, target, size_average=False).item()
            pred = output.max(1, keepdim=True)[1]
            correct += pred.eq(target.view_as(pred)).sum().item()
    test_loss /= len(test_loader.dataset)
    print('Test: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)'.format(
        test_loss, correct, len(test_loader.dataset),
        100. * correct / len(test_loader.dataset)))
    test_accuracy = correct / len(test_loader.dataset)
    return test_loss, test_accuracy


def adjust_learning_rate(optimizer, epoch):
    """learning rate schedule with warmup"""
    if epoch < args.warmup_epochs:
        # 线性warm-up
        lr = args.lr * (epoch + 1) / args.warmup_epochs
    else:
        # 余弦衰减策略
        lr = args.lr * 0.5 * (1 + math.cos(math.pi * (epoch - args.warmup_epochs) / (args.epochs - args.warmup_epochs)))
        
        # 或者使用阶梯式衰减
        # if epoch >= 75:
        #     lr = args.lr * 0.1
        # if epoch >= 90:
        #     lr = args.lr * 0.01
        # if epoch >= 100:
        #     lr = args.lr * 0.001
    
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
    
    return lr

def main():
    # 根据选择加载不同的模型
    if args.model == 'resnet18':
        from models.resnet import ResNet18
        # 修改输入通道数以匹配FashionMNIST
        model = ResNet18(num_classes=10, in_channels=1).to(device)
        model_name = 'resnet18'
    elif args.model == 'resnet50':
        from models.resnet import ResNet50
        model = ResNet50(num_classes=10, in_channels=1).to(device)
        model_name = 'resnet50'
    elif args.model == 'wide_resnet34_10':
        from models.wideresn import WideResNet
        model = WideResNet(depth=34, num_classes=10, widen_factor=10, in_channels=1).to(device)
        model_name = 'wrn34_10'
    elif args.model == 'densenet121':
        from models.densenet import DenseNet121
        model = DenseNet121(num_classes=10, in_channels=1).to(device)
        model_name = 'densenet121'
    else:  # 默认SmallCNN
        from models.small_cnn import SmallCNN
        model = SmallCNN().to(device)
        model_name = 'small_cnn'
    
    optimizer = optim.SGD(model.parameters(), lr=args.lr, momentum=args.momentum, weight_decay=args.weight_decay)
    
    # 如果使用混合精度，打印提示信息
    if args.amp:
        print("Using automatic mixed precision training")
        
    # 创建包含详细信息的模型保存文件夹
    dataset_name = "fashion_mnist"
    current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = os.path.join(args.model_dir, f'{dataset_name}_{model_name}_bs{args.batch_size}_lr{args.lr}_{current_time}')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    print(f'模型将保存到: {save_dir}')

    for epoch in range(1, args.epochs + 1):
        # 调整学习率并记录
        current_lr = adjust_learning_rate(optimizer, epoch)
        print(f'当前学习率: {current_lr:.6f}')

        # adversarial training
        train(args, model, device, train_loader, optimizer, epoch)

        # evaluation on natural examples
        print('================================================================')
        eval_train(model, device, train_loader)
        eval_test(model, device, test_loader)
        print('================================================================')

        # save checkpoint
        if epoch % args.save_freq == 0:
            torch.save(model.state_dict(),
                       os.path.join(save_dir, f'model-{model_name}-epoch{epoch}.pt'))
            torch.save(optimizer.state_dict(),
                       os.path.join(save_dir, f'opt-{model_name}-checkpoint_epoch{epoch}.tar'))


if __name__ == '__main__':
    main()
