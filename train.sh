#!/bin/bash

# 灵活的TRADES训练脚本
# 使用方法: ./train.sh --dataset [cifar10|cifar100|svhn|fashion_mnist] --model [resnet18|resnet50|wide_resnet34_10|densenet121] --batch_size [size] --lr [learning_rate] [--amp] [--clip_grad [value]]

# 默认参数
DATASET="cifar10"
MODEL="resnet50"
BATCH_SIZE=64
LR=0.01
USE_AMP=0
CLIP_GRAD=1.0
USE_CLIP_GRAD=0

# 参数解析
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --dataset)
      DATASET="$2"
      shift 2
      ;;
    --model)
      MODEL="$2"
      shift 2
      ;;
    --batch_size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --lr)
      LR="$2"
      shift 2
      ;;
    --amp)
      USE_AMP=1
      shift
      ;;
    --clip_grad)
      if [[ $2 =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        CLIP_GRAD="$2"
        USE_CLIP_GRAD=1
        shift 2
      else
        USE_CLIP_GRAD=1
        shift
      fi
      ;;
    *)
      echo "未知选项: $1"
      exit 1
      ;;
  esac
done

# 通用参数
MOMENTUM=0.9
WEIGHT_DECAY=5e-4
EPSILON=0.031
STEP_SIZE=0.007843137
NUM_STEPS=20
BETA=6.0
EPOCHS=100
SAVE_FREQ=10

# 创建日志目录
LOG_DIR="training_logs"
mkdir -p $LOG_DIR

# 设置混合精度标志
AMP_FLAG=""
if [ $USE_AMP -eq 1 ]; then
  AMP_FLAG="--amp"
fi

# 设置梯度裁剪标志
CLIP_GRAD_FLAG=""
if [ $USE_CLIP_GRAD -eq 1 ]; then
  CLIP_GRAD_FLAG="--clip-grad $CLIP_GRAD"
fi

# 选择正确的训练脚本和日志文件名
case $DATASET in
  cifar10)
    SCRIPT="train_trades_cifar10_3.py"
    ;;
  cifar100)
    SCRIPT="train_trades_cifar100_2.py"
    ;;
  svhn)
    SCRIPT="train_trades_svhn.py"
    ;;
  fashion_mnist)
    SCRIPT="train_trades_fashion_mnist.py"
    ;;
  *)
    echo "不支持的数据集: $DATASET"
    echo "支持的数据集: cifar10, cifar100, svhn, fashion_mnist"
    exit 1
    ;;
esac

# 构建日志文件名
DATE=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${DATASET}_${MODEL}_bs${BATCH_SIZE}_lr${LR}_${DATE}.log"

# 将训练配置保存到日志文件
{
  echo "===================================================="
  echo "                  TRADES 训练配置                    "
  echo "===================================================="
  echo "日期时间: $(date)"
  echo "数据集: $DATASET"
  echo "模型: $MODEL"
  echo "批大小: $BATCH_SIZE"
  echo "学习率: $LR"
  echo "动量: $MOMENTUM"
  echo "权重衰减: $WEIGHT_DECAY"
  echo "epsilon: $EPSILON"
  echo "步长: $STEP_SIZE"
  echo "扰动步数: $NUM_STEPS"
  echo "正则化系数beta: $BETA"
  echo "训练轮数: $EPOCHS"
  echo "保存频率: $SAVE_FREQ"
  echo "混合精度训练: $([ $USE_AMP -eq 1 ] && echo "开启" || echo "关闭")"
  echo "梯度裁剪: $([ $USE_CLIP_GRAD -eq 1 ] && echo "开启" || echo "关闭")"
  echo "裁剪梯度值: $CLIP_GRAD"
  echo "训练脚本: $SCRIPT"
  echo "===================================================="
  echo ""
} > "$LOG_DIR/$LOG_FILE"

# 显示训练配置
echo "===== 训练配置 ====="
echo "数据集: $DATASET"
echo "模型: $MODEL"
echo "批大小: $BATCH_SIZE"
echo "学习率: $LR"
echo "混合精度: $([ $USE_AMP -eq 1 ] && echo "开启" || echo "关闭")"
echo "梯度裁剪: $([ $USE_CLIP_GRAD -eq 1 ] && echo "开启" || echo "关闭")"
echo "裁剪梯度值: $CLIP_GRAD"
echo "日志文件: $LOG_DIR/$LOG_FILE"
echo "===================="

# 执行训练命令
echo "开始训练..."
python $SCRIPT \
--batch-size $BATCH_SIZE \
--lr $LR \
--momentum $MOMENTUM \
--weight-decay $WEIGHT_DECAY \
--epsilon $EPSILON \
--step-size $STEP_SIZE \
--num-steps $NUM_STEPS \
--beta $BETA \
--epochs $EPOCHS \
--save-freq $SAVE_FREQ \
--model $MODEL \
$AMP_FLAG $CLIP_GRAD_FLAG 2>&1 | tee -a "$LOG_DIR/$LOG_FILE"

echo "训练完成! 日志已保存至 $LOG_DIR/$LOG_FILE" 