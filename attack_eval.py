from __future__ import print_function
import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import numpy as np
from torch.autograd import Variable
import torch.optim as optim
from torchvision import datasets, transforms
from models.wideresnet import *
from models.resnet import *
from models.densenet import *
from tqdm import tqdm

# 导入torchattacks库
try:
    import torchattacks
except ImportError:
    print("torchattacks not found. Please install it with: pip install torchattacks")


parser = argparse.ArgumentParser(description='PyTorch Model Robustness Evaluation')
parser.add_argument('--batch-size', type=int, default=128, metavar='N',
                    help='input batch size for testing (default: 128)')
parser.add_argument('--no-cuda', action='store_true', default=False,
                    help='disables CUDA training')
parser.add_argument('--epsilon', type=float, default=0.031,
                    help='perturbation bound (default: 8/255)')
parser.add_argument('--pgd-alpha', type=float, default=0.007,
                    help='PGD step size (default: 2/255)')
parser.add_argument('--pgd-steps', type=int, default=20,
                    help='number of PGD iterations for attack (default: 20)')
parser.add_argument('--model-path', type=str, required=True,
                    help='path to trained model')
parser.add_argument('--model', type=str, default='resnet18',
                    help='model architecture (resnet18, resnet50, wide_resnet34_10, densenet121)')
parser.add_argument('--dataset', type=str, default='cifar10',
                    choices=['cifar10', 'cifar100', 'svhn', 'fashion_mnist', 'caltech101'],
                    help='dataset to use for evaluation')
parser.add_argument('--data-dir', type=str, default='./data',
                    help='directory where datasets are stored')
parser.add_argument('--attack', type=str, default='all',
                    choices=['clean', 'fgsm', 'pgd', 'cw', 'aa', 'all'],
                    help='attack method to evaluate')

args = parser.parse_args()

# 配置
use_cuda = not args.no_cuda and torch.cuda.is_available()
device = torch.device("cuda" if use_cuda else "cpu")
kwargs = {'num_workers': 4, 'pin_memory': True} if use_cuda else {}

# 加载数据集
def load_dataset(dataset_name, data_dir):
    if dataset_name == 'cifar10':
        num_classes = 10
        in_channels = 3
        transform_test = transforms.Compose([
            transforms.ToTensor(),
        ])
        testset = torchvision.datasets.CIFAR10(root=data_dir, train=False, download=True, transform=transform_test)
    
    elif dataset_name == 'cifar100':
        num_classes = 100
        in_channels = 3
        transform_test = transforms.Compose([
            transforms.ToTensor(),
        ])
        testset = torchvision.datasets.CIFAR100(root=data_dir, train=False, download=True, transform=transform_test)
    
    elif dataset_name == 'svhn':
        num_classes = 10
        in_channels = 3
        transform_test = transforms.Compose([
            transforms.ToTensor(),
        ])
        testset = torchvision.datasets.SVHN(root=data_dir, split='test', download=True, transform=transform_test)
    
    elif dataset_name == 'fashion_mnist':
        num_classes = 10
        in_channels = 1
        transform_test = transforms.Compose([
            transforms.ToTensor(),
        ])
        testset = torchvision.datasets.FashionMNIST(root=data_dir, train=False, download=True, transform=transform_test)
    
    elif dataset_name == 'caltech101':
        num_classes = 101
        in_channels = 3
        transform_test = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
        ])
        dataset = torchvision.datasets.Caltech101(root=data_dir, download=True, transform=transform_test)
        
        # 对于Caltech101，我们需要手动分割训练集和测试集
        from torch.utils.data import random_split
        test_size = int(0.2 * len(dataset))  # 20% 作为测试集
        train_size = len(dataset) - test_size
        _, testset = random_split(dataset, [train_size, test_size])
    
    else:
        raise ValueError(f"Dataset {dataset_name} not supported")
    
    return testset, num_classes, in_channels

# 加载模型
def load_model(model_name, model_path, num_classes, in_channels):
    if model_name == 'resnet18':
        model = ResNet18(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'resnet50':
        model = ResNet50(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'wide_resnet34_10':
        model = WideResNet(depth=34, num_classes=num_classes, widen_factor=10, in_channels=in_channels)
    elif model_name == 'densenet121':
        model = DenseNet121(num_classes=num_classes, in_channels=in_channels)
    else:
        raise ValueError(f"Model {model_name} not supported")
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    return model

# 计算准确率
def accuracy(output, target):
    pred = output.max(1, keepdim=True)[1]
    correct = pred.eq(target.view_as(pred)).sum().item()
    return correct

# 使用torchattacks库评估模型鲁棒性
def evaluate(model, test_loader, attack_type='clean', epsilon=8/255, alpha=2/255, steps=20, batch_size=128):
    model.eval()
    test_loss = 0
    correct = 0
    total = 0
    
    # 如果是CW攻击，检查batch_size是否过大
    if attack_type == 'cw' and batch_size > 64:
        print("警告: CW攻击内存占用较大，建议使用更小的batch size (例如 --batch-size 32)")
    
    # 设置攻击方法
    attack = None
    if attack_type != 'clean':
        if attack_type == 'fgsm':
            attack = torchattacks.FGSM(model, eps=epsilon)
        elif attack_type == 'pgd':
            attack = torchattacks.PGD(model, eps=epsilon, alpha=alpha, steps=steps, random_start=True)
        elif attack_type == 'cw':
            attack = torchattacks.CW(model, c=1, kappa=0, steps=100, lr=0.01)
        elif attack_type == 'aa':
            attack = torchattacks.AutoAttack(model, norm='Linf', eps=epsilon, version='standard', verbose=False)
    
    # 评估模型
    pbar = tqdm(test_loader, desc=f"Evaluating {attack_type.upper()}")
    for images, labels in pbar:
        images, labels = images.to(device), labels.to(device)
        
        # 应用攻击（如果不是clean评估）
        if attack is not None:
            images = attack(images, labels)
        
        # 模型预测
        with torch.no_grad():
            outputs = model(images)
            loss = F.cross_entropy(outputs, labels)
            
            # 统计结果
            test_loss += loss.item() * images.shape[0]
            correct += accuracy(outputs, labels)
            total += images.shape[0]
            
            # 更新进度条显示当前准确率
            current_acc = 100. * correct / total
            pbar.set_postfix({'accuracy': f'{current_acc:.2f}%'})
    
    test_loss /= total
    acc = 100. * correct / total
    
    return test_loss, acc

def main():
    # 加载数据集
    testset, num_classes, in_channels = load_dataset(args.dataset, args.data_dir)
    test_loader = torch.utils.data.DataLoader(testset, batch_size=args.batch_size, 
                                              shuffle=False, **kwargs)
    
    # 加载模型
    model = load_model(args.model, args.model_path, num_classes, in_channels)
    
    # 攻击参数
    epsilon = args.epsilon
    pgd_alpha = args.pgd_alpha
    pgd_steps = args.pgd_steps
    
    # 评估鲁棒性
    results = {}
    
    print(f"\nEvaluating {args.model} on {args.dataset} dataset with epsilon={epsilon}")
    print("="*60)
    
    if args.attack == 'clean' or args.attack == 'all':
        loss, acc = evaluate(model, test_loader, attack_type='clean', batch_size=args.batch_size)
        results['clean'] = (loss, acc)
        print(f"Clean accuracy: {acc:.2f}%")
    
    if args.attack == 'fgsm' or args.attack == 'all':
        loss, acc = evaluate(model, test_loader, attack_type='fgsm', epsilon=epsilon, batch_size=args.batch_size)
        results['fgsm'] = (loss, acc)
        print(f"FGSM attack accuracy: {acc:.2f}%")
    
    if args.attack == 'pgd' or args.attack == 'all':
        loss, acc = evaluate(model, test_loader, attack_type='pgd', 
                            epsilon=epsilon, alpha=pgd_alpha, steps=pgd_steps, batch_size=args.batch_size)
        results['pgd'] = (loss, acc)
        print(f"PGD attack accuracy: {acc:.2f}%")
    
    if args.attack == 'cw' or args.attack == 'all':
        loss, acc = evaluate(model, test_loader, attack_type='cw', epsilon=epsilon, batch_size=args.batch_size)
        results['cw'] = (loss, acc)
        print(f"CW attack accuracy: {acc:.2f}%")
    
    if args.attack == 'aa' or args.attack == 'all':
        try:
            loss, acc = evaluate(model, test_loader, attack_type='aa', epsilon=epsilon, batch_size=args.batch_size)
            results['aa'] = (loss, acc)
            print(f"AutoAttack accuracy: {acc:.2f}%")
        except NameError:
            print("AutoAttack not available. Please install torchattacks first.")
    
    print("="*60)
    print("\nSummary:")
    for attack_type, (loss, acc) in results.items():
        print(f"{attack_type.upper()} - Loss: {loss:.4f}, Accuracy: {acc:.2f}%")

if __name__ == "__main__":
    main() 