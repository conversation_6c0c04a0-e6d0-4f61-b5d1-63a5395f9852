python train_trades_cifar10.py \
    --batch-size 128 \
    --lr 0.1 \
    --momentum 0.9 \
    --weight-decay 5e-4 \
    --epsilon 0.031 \
    --step-size 0.007843137 \
    --num-steps 20 \
    --beta 6.0 \
    --epochs 10 \
    --save-freq 10 \
    --model resnet18

python train.py \
    --batch-size 256 \
    --lr 0.01 \
    --momentum 0.9 \
    --weight-decay 5e-4 \
    --epsilon 0.031 \
    --step-size 0.007843137 \
    --num-steps 20 \
    --beta 6.0 \
    --epochs 100 \
    --save-freq 10 \
    --model resnet18 \
    --dataset cifar10 