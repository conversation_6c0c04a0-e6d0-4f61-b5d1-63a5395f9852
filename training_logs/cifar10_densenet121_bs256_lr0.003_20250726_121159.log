====================================================
                  TRADES 训练配置                    
====================================================
日期时间: Sat 26 Jul 2025 12:11:59 PM UTC
数据集: cifar10
模型: densenet121
批大小: 256
学习率: 0.003
动量: 0.9
权重衰减: 5e-4
epsilon: 0.031
步长: 0.007843137
扰动步数: 20
正则化系数beta: 6.0
训练轮数: 100
保存频率: 10
混合精度训练: 开启
梯度裁剪: 关闭
裁剪梯度值: 1.0
训练脚本: train_trades_cifar10_3.py
====================================================

Files already downloaded and verified
Files already downloaded and verified
Using automatic mixed precision training
模型将保存到: ./checkpoints/cifar10_densenet121_bs256_lr0.003_20250726_121213
train_trades_cifar10_3.py:89: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler() if args.amp else None
Train Epoch: 1 [0/50000 (0%)]	Loss: 2.323905
Train Epoch: 1 [25600/50000 (51%)]	Loss: 2.393817
================================================================
train_trades_cifar10_3.py:98: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/_reduction.py:42: UserWarning: size_average and reduce args will be deprecated, please use reduction='sum' instead.
  warnings.warn(warning.format(ret))
Training: Average loss: 2.2469, Accuracy: 9149/50000 (18%)
Test: Average loss: 2.2445, Accuracy: 1841/10000 (18%)
================================================================
Train Epoch: 2 [0/50000 (0%)]	Loss: 2.465943
Train Epoch: 2 [25600/50000 (51%)]	Loss: 2.317446
================================================================
Training: Average loss: 2.1709, Accuracy: 11868/50000 (24%)
Test: Average loss: 2.1591, Accuracy: 2380/10000 (24%)
================================================================
Train Epoch: 3 [0/50000 (0%)]	Loss: 2.291475
Train Epoch: 3 [25600/50000 (51%)]	Loss: 2.216997
================================================================
Training: Average loss: 2.0959, Accuracy: 14203/50000 (28%)
Test: Average loss: 2.0761, Accuracy: 2793/10000 (28%)
================================================================
Train Epoch: 4 [0/50000 (0%)]	Loss: 2.173184
Train Epoch: 4 [25600/50000 (51%)]	Loss: 2.187755
================================================================
Training: Average loss: 2.0429, Accuracy: 15142/50000 (30%)
Test: Average loss: 2.0204, Accuracy: 2974/10000 (30%)
================================================================
Train Epoch: 5 [0/50000 (0%)]	Loss: 2.103285
Train Epoch: 5 [25600/50000 (51%)]	Loss: 2.089190
================================================================
Training: Average loss: 2.0038, Accuracy: 16218/50000 (32%)
Test: Average loss: 1.9797, Accuracy: 3247/10000 (32%)
================================================================
Train Epoch: 6 [0/50000 (0%)]	Loss: 2.129877
Train Epoch: 6 [25600/50000 (51%)]	Loss: 2.078376
================================================================
Training: Average loss: 1.9789, Accuracy: 16440/50000 (33%)
Test: Average loss: 1.9515, Accuracy: 3325/10000 (33%)
================================================================
Train Epoch: 7 [0/50000 (0%)]	Loss: 2.066832
Train Epoch: 7 [25600/50000 (51%)]	Loss: 2.076472
================================================================
Training: Average loss: 1.9465, Accuracy: 16930/50000 (34%)
Test: Average loss: 1.9197, Accuracy: 3420/10000 (34%)
================================================================
Train Epoch: 8 [0/50000 (0%)]	Loss: 2.074167
Train Epoch: 8 [25600/50000 (51%)]	Loss: 2.122905
================================================================
Training: Average loss: 1.9202, Accuracy: 17728/50000 (35%)
Test: Average loss: 1.8858, Accuracy: 3638/10000 (36%)
================================================================
Train Epoch: 9 [0/50000 (0%)]	Loss: 1.992603
Train Epoch: 9 [25600/50000 (51%)]	Loss: 1.986142
================================================================
Training: Average loss: 1.8914, Accuracy: 18092/50000 (36%)
Test: Average loss: 1.8619, Accuracy: 3560/10000 (36%)
================================================================
Train Epoch: 10 [0/50000 (0%)]	Loss: 2.021686
Train Epoch: 10 [25600/50000 (51%)]	Loss: 1.968404
================================================================
Training: Average loss: 1.8679, Accuracy: 18438/50000 (37%)
Test: Average loss: 1.8352, Accuracy: 3752/10000 (38%)
================================================================
Train Epoch: 11 [0/50000 (0%)]	Loss: 1.997673
Train Epoch: 11 [25600/50000 (51%)]	Loss: 1.990944
================================================================
Training: Average loss: 1.8412, Accuracy: 19037/50000 (38%)
Test: Average loss: 1.8139, Accuracy: 3815/10000 (38%)
================================================================
Train Epoch: 12 [0/50000 (0%)]	Loss: 2.038108
Train Epoch: 12 [25600/50000 (51%)]	Loss: 1.963618
================================================================
Training: Average loss: 1.8132, Accuracy: 19733/50000 (39%)
Test: Average loss: 1.7814, Accuracy: 3946/10000 (39%)
================================================================
Train Epoch: 13 [0/50000 (0%)]	Loss: 1.912291
Train Epoch: 13 [25600/50000 (51%)]	Loss: 1.935825
================================================================
Training: Average loss: 1.8003, Accuracy: 19925/50000 (40%)
Test: Average loss: 1.7611, Accuracy: 3985/10000 (40%)
================================================================
Train Epoch: 14 [0/50000 (0%)]	Loss: 1.945611
Train Epoch: 14 [25600/50000 (51%)]	Loss: 1.922156
================================================================
Training: Average loss: 1.7804, Accuracy: 20253/50000 (41%)
Test: Average loss: 1.7511, Accuracy: 4019/10000 (40%)
================================================================
Train Epoch: 15 [0/50000 (0%)]	Loss: 1.863925
Train Epoch: 15 [25600/50000 (51%)]	Loss: 1.937623
================================================================
Training: Average loss: 1.7725, Accuracy: 20060/50000 (40%)
Test: Average loss: 1.7404, Accuracy: 4037/10000 (40%)
================================================================
Train Epoch: 16 [0/50000 (0%)]	Loss: 1.961470
Train Epoch: 16 [25600/50000 (51%)]	Loss: 1.926911
================================================================
Training: Average loss: 1.7554, Accuracy: 20759/50000 (42%)
Test: Average loss: 1.7170, Accuracy: 4154/10000 (42%)
================================================================
Train Epoch: 17 [0/50000 (0%)]	Loss: 1.915949
Train Epoch: 17 [25600/50000 (51%)]	Loss: 1.845831
================================================================
Training: Average loss: 1.7460, Accuracy: 21361/50000 (43%)
Test: Average loss: 1.7038, Accuracy: 4307/10000 (43%)
================================================================
Train Epoch: 18 [0/50000 (0%)]	Loss: 1.902185
Train Epoch: 18 [25600/50000 (51%)]	Loss: 1.837615
================================================================
Training: Average loss: 1.7371, Accuracy: 21186/50000 (42%)
Test: Average loss: 1.7109, Accuracy: 4241/10000 (42%)
================================================================
Train Epoch: 19 [0/50000 (0%)]	Loss: 1.904016
Train Epoch: 19 [25600/50000 (51%)]	Loss: 1.840894
================================================================
Training: Average loss: 1.7279, Accuracy: 21185/50000 (42%)
Test: Average loss: 1.6908, Accuracy: 4255/10000 (43%)
================================================================
Train Epoch: 20 [0/50000 (0%)]	Loss: 1.860079
Train Epoch: 20 [25600/50000 (51%)]	Loss: 1.877193
================================================================
Training: Average loss: 1.7038, Accuracy: 21963/50000 (44%)
Test: Average loss: 1.6679, Accuracy: 4404/10000 (44%)
================================================================
Train Epoch: 21 [0/50000 (0%)]	Loss: 1.799938
Train Epoch: 21 [25600/50000 (51%)]	Loss: 1.858682
================================================================
Training: Average loss: 1.7055, Accuracy: 21656/50000 (43%)
Test: Average loss: 1.6714, Accuracy: 4347/10000 (43%)
================================================================
Train Epoch: 22 [0/50000 (0%)]	Loss: 1.859219
Train Epoch: 22 [25600/50000 (51%)]	Loss: 1.819013
================================================================
Training: Average loss: 1.6922, Accuracy: 22026/50000 (44%)
Test: Average loss: 1.6601, Accuracy: 4437/10000 (44%)
================================================================
Train Epoch: 23 [0/50000 (0%)]	Loss: 1.809820
Train Epoch: 23 [25600/50000 (51%)]	Loss: 1.853605
================================================================
Training: Average loss: 1.6883, Accuracy: 22738/50000 (45%)
Test: Average loss: 1.6572, Accuracy: 4500/10000 (45%)
================================================================
Train Epoch: 24 [0/50000 (0%)]	Loss: 1.830463
Train Epoch: 24 [25600/50000 (51%)]	Loss: 1.873845
================================================================
Training: Average loss: 1.6865, Accuracy: 22177/50000 (44%)
Test: Average loss: 1.6464, Accuracy: 4426/10000 (44%)
================================================================
Train Epoch: 25 [0/50000 (0%)]	Loss: 1.909200
Train Epoch: 25 [25600/50000 (51%)]	Loss: 1.851160
================================================================
Training: Average loss: 1.6675, Accuracy: 22531/50000 (45%)
Test: Average loss: 1.6358, Accuracy: 4518/10000 (45%)
================================================================
Train Epoch: 26 [0/50000 (0%)]	Loss: 1.799161
Train Epoch: 26 [25600/50000 (51%)]	Loss: 1.841675
================================================================
Training: Average loss: 1.6597, Accuracy: 22706/50000 (45%)
Test: Average loss: 1.6250, Accuracy: 4601/10000 (46%)
================================================================
Train Epoch: 27 [0/50000 (0%)]	Loss: 1.836847
Train Epoch: 27 [25600/50000 (51%)]	Loss: 1.796564
================================================================
Training: Average loss: 1.6563, Accuracy: 22613/50000 (45%)
Test: Average loss: 1.6211, Accuracy: 4495/10000 (45%)
================================================================
Train Epoch: 28 [0/50000 (0%)]	Loss: 1.778348
Train Epoch: 28 [25600/50000 (51%)]	Loss: 1.853934
================================================================
Training: Average loss: 1.6508, Accuracy: 23050/50000 (46%)
Test: Average loss: 1.6212, Accuracy: 4556/10000 (46%)
================================================================
