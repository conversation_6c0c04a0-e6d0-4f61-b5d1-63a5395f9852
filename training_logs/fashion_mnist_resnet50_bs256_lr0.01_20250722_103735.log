====================================================
                  TRADES 训练配置                    
====================================================
日期时间: Tue 22 Jul 2025 10:37:35 AM UTC
数据集: fashion_mnist
模型: resnet50
批大小: 256
学习率: 0.01
动量: 0.9
权重衰减: 5e-4
epsilon: 0.031
步长: 0.007843137
扰动步数: 20
正则化系数beta: 6.0
训练轮数: 100
保存频率: 10
混合精度训练: 开启
训练脚本: train_trades_fashion_mnist.py
====================================================

Using automatic mixed precision training
模型将保存到: ./checkpoints/fashion_mnist_resnet50_bs256_lr0.01_20250722_103743
当前学习率: 0.004000
train_trades_fashion_mnist.py:84: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler() if args.amp else None
Train Epoch: 1 [0/60000 (0%)]	Loss: 2.416323
Train Epoch: 1 [25600/60000 (43%)]	Loss: 0.751579
Train Epoch: 1 [51200/60000 (85%)]	Loss: 0.537197
================================================================
train_trades_fashion_mnist.py:93: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/_reduction.py:42: UserWarning: size_average and reduce args will be deprecated, please use reduction='sum' instead.
  warnings.warn(warning.format(ret))
Training: Average loss: 0.4464, Accuracy: 49697/60000 (83%)
Test: Average loss: 0.4765, Accuracy: 8130/10000 (81%)
================================================================
当前学习率: 0.006000
Train Epoch: 2 [0/60000 (0%)]	Loss: 0.561618
Train Epoch: 2 [25600/60000 (43%)]	Loss: 0.573871
Train Epoch: 2 [51200/60000 (85%)]	Loss: 0.438586
================================================================
Training: Average loss: 0.3532, Accuracy: 52513/60000 (88%)
Test: Average loss: 0.3866, Accuracy: 8579/10000 (86%)
================================================================
当前学习率: 0.008000
Train Epoch: 3 [0/60000 (0%)]	Loss: 0.556058
Train Epoch: 3 [25600/60000 (43%)]	Loss: 0.512388
Train Epoch: 3 [51200/60000 (85%)]	Loss: 0.420339
================================================================
Training: Average loss: 0.3088, Accuracy: 53061/60000 (88%)
Test: Average loss: 0.3538, Accuracy: 8662/10000 (87%)
================================================================
当前学习率: 0.010000
Train Epoch: 4 [0/60000 (0%)]	Loss: 0.413706
Train Epoch: 4 [25600/60000 (43%)]	Loss: 0.416656
Train Epoch: 4 [51200/60000 (85%)]	Loss: 0.356092
================================================================
Training: Average loss: 0.3291, Accuracy: 52295/60000 (87%)
Test: Average loss: 0.3794, Accuracy: 8528/10000 (85%)
================================================================
当前学习率: 0.010000
Train Epoch: 5 [0/60000 (0%)]	Loss: 0.407027
Train Epoch: 5 [25600/60000 (43%)]	Loss: 0.333926
Train Epoch: 5 [51200/60000 (85%)]	Loss: 0.414889
================================================================
Training: Average loss: 0.2715, Accuracy: 53985/60000 (90%)
Test: Average loss: 0.3214, Accuracy: 8764/10000 (88%)
================================================================
当前学习率: 0.009997
Train Epoch: 6 [0/60000 (0%)]	Loss: 0.255133
Train Epoch: 6 [25600/60000 (43%)]	Loss: 0.348945
Train Epoch: 6 [51200/60000 (85%)]	Loss: 0.339477
================================================================
Training: Average loss: 0.2466, Accuracy: 54481/60000 (91%)
Test: Average loss: 0.3068, Accuracy: 8830/10000 (88%)
================================================================
当前学习率: 0.009989
Train Epoch: 7 [0/60000 (0%)]	Loss: 0.351684
Train Epoch: 7 [25600/60000 (43%)]	Loss: 0.412958
Train Epoch: 7 [51200/60000 (85%)]	Loss: 0.363007
================================================================
Training: Average loss: 0.3151, Accuracy: 52785/60000 (88%)
Test: Average loss: 0.3772, Accuracy: 8614/10000 (86%)
================================================================
当前学习率: 0.009975
Train Epoch: 8 [0/60000 (0%)]	Loss: 0.352272
Train Epoch: 8 [25600/60000 (43%)]	Loss: 0.322066
Train Epoch: 8 [51200/60000 (85%)]	Loss: 0.436824
================================================================
Training: Average loss: 0.3742, Accuracy: 51461/60000 (86%)
Test: Average loss: 0.4395, Accuracy: 8412/10000 (84%)
================================================================
当前学习率: 0.009956
Train Epoch: 9 [0/60000 (0%)]	Loss: 0.484787
Train Epoch: 9 [25600/60000 (43%)]	Loss: 0.428549
Train Epoch: 9 [51200/60000 (85%)]	Loss: 0.620303
================================================================
Training: Average loss: 0.3734, Accuracy: 51445/60000 (86%)
Test: Average loss: 0.4370, Accuracy: 8401/10000 (84%)
================================================================
当前学习率: 0.009932
Train Epoch: 10 [0/60000 (0%)]	Loss: 0.364262
Train Epoch: 10 [25600/60000 (43%)]	Loss: 0.364781
Train Epoch: 10 [51200/60000 (85%)]	Loss: 0.432228
================================================================
Training: Average loss: 0.3861, Accuracy: 51220/60000 (85%)
Test: Average loss: 0.4507, Accuracy: 8366/10000 (84%)
================================================================
当前学习率: 0.009902
Train Epoch: 11 [0/60000 (0%)]	Loss: 0.434290
Train Epoch: 11 [25600/60000 (43%)]	Loss: 0.569759
Train Epoch: 11 [51200/60000 (85%)]	Loss: 0.513989
================================================================
Training: Average loss: 0.3703, Accuracy: 51632/60000 (86%)
Test: Average loss: 0.4323, Accuracy: 8442/10000 (84%)
================================================================
当前学习率: 0.009867
Train Epoch: 12 [0/60000 (0%)]	Loss: 0.421911
Train Epoch: 12 [25600/60000 (43%)]	Loss: 0.475192
Train Epoch: 12 [51200/60000 (85%)]	Loss: 0.565312
================================================================
Training: Average loss: 0.3639, Accuracy: 51678/60000 (86%)
Test: Average loss: 0.4270, Accuracy: 8436/10000 (84%)
================================================================
当前学习率: 0.009826
Train Epoch: 13 [0/60000 (0%)]	Loss: 0.449473
Train Epoch: 13 [25600/60000 (43%)]	Loss: 0.443907
Train Epoch: 13 [51200/60000 (85%)]	Loss: 0.567932
================================================================
Training: Average loss: 0.3751, Accuracy: 51466/60000 (86%)
Test: Average loss: 0.4378, Accuracy: 8407/10000 (84%)
================================================================
当前学习率: 0.009780
Train Epoch: 14 [0/60000 (0%)]	Loss: 0.498866
Train Epoch: 14 [25600/60000 (43%)]	Loss: 0.401975
Train Epoch: 14 [51200/60000 (85%)]	Loss: 0.457693
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009729
Train Epoch: 15 [0/60000 (0%)]	Loss: nan
Train Epoch: 15 [25600/60000 (43%)]	Loss: nan
Train Epoch: 15 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009673
Train Epoch: 16 [0/60000 (0%)]	Loss: nan
Train Epoch: 16 [25600/60000 (43%)]	Loss: nan
Train Epoch: 16 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009611
Train Epoch: 17 [0/60000 (0%)]	Loss: nan
Train Epoch: 17 [25600/60000 (43%)]	Loss: nan
Train Epoch: 17 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009545
Train Epoch: 18 [0/60000 (0%)]	Loss: nan
Train Epoch: 18 [25600/60000 (43%)]	Loss: nan
Train Epoch: 18 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009474
Train Epoch: 19 [0/60000 (0%)]	Loss: nan
Train Epoch: 19 [25600/60000 (43%)]	Loss: nan
Train Epoch: 19 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009397
Train Epoch: 20 [0/60000 (0%)]	Loss: nan
Train Epoch: 20 [25600/60000 (43%)]	Loss: nan
Train Epoch: 20 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009316
Train Epoch: 21 [0/60000 (0%)]	Loss: nan
Train Epoch: 21 [25600/60000 (43%)]	Loss: nan
Train Epoch: 21 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009230
Train Epoch: 22 [0/60000 (0%)]	Loss: nan
Train Epoch: 22 [25600/60000 (43%)]	Loss: nan
Train Epoch: 22 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009140
Train Epoch: 23 [0/60000 (0%)]	Loss: nan
Train Epoch: 23 [25600/60000 (43%)]	Loss: nan
Train Epoch: 23 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.009045
Train Epoch: 24 [0/60000 (0%)]	Loss: nan
Train Epoch: 24 [25600/60000 (43%)]	Loss: nan
Train Epoch: 24 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008946
Train Epoch: 25 [0/60000 (0%)]	Loss: nan
Train Epoch: 25 [25600/60000 (43%)]	Loss: nan
Train Epoch: 25 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008842
Train Epoch: 26 [0/60000 (0%)]	Loss: nan
Train Epoch: 26 [25600/60000 (43%)]	Loss: nan
Train Epoch: 26 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008734
Train Epoch: 27 [0/60000 (0%)]	Loss: nan
Train Epoch: 27 [25600/60000 (43%)]	Loss: nan
Train Epoch: 27 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008622
Train Epoch: 28 [0/60000 (0%)]	Loss: nan
Train Epoch: 28 [25600/60000 (43%)]	Loss: nan
Train Epoch: 28 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008506
Train Epoch: 29 [0/60000 (0%)]	Loss: nan
Train Epoch: 29 [25600/60000 (43%)]	Loss: nan
Train Epoch: 29 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008386
Train Epoch: 30 [0/60000 (0%)]	Loss: nan
Train Epoch: 30 [25600/60000 (43%)]	Loss: nan
Train Epoch: 30 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008263
Train Epoch: 31 [0/60000 (0%)]	Loss: nan
Train Epoch: 31 [25600/60000 (43%)]	Loss: nan
Train Epoch: 31 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008136
Train Epoch: 32 [0/60000 (0%)]	Loss: nan
Train Epoch: 32 [25600/60000 (43%)]	Loss: nan
Train Epoch: 32 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.008005
Train Epoch: 33 [0/60000 (0%)]	Loss: nan
Train Epoch: 33 [25600/60000 (43%)]	Loss: nan
Train Epoch: 33 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007872
Train Epoch: 34 [0/60000 (0%)]	Loss: nan
Train Epoch: 34 [25600/60000 (43%)]	Loss: nan
Train Epoch: 34 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007735
Train Epoch: 35 [0/60000 (0%)]	Loss: nan
Train Epoch: 35 [25600/60000 (43%)]	Loss: nan
Train Epoch: 35 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007595
Train Epoch: 36 [0/60000 (0%)]	Loss: nan
Train Epoch: 36 [25600/60000 (43%)]	Loss: nan
Train Epoch: 36 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007452
Train Epoch: 37 [0/60000 (0%)]	Loss: nan
Train Epoch: 37 [25600/60000 (43%)]	Loss: nan
Train Epoch: 37 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007307
Train Epoch: 38 [0/60000 (0%)]	Loss: nan
Train Epoch: 38 [25600/60000 (43%)]	Loss: nan
Train Epoch: 38 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007159
Train Epoch: 39 [0/60000 (0%)]	Loss: nan
Train Epoch: 39 [25600/60000 (43%)]	Loss: nan
Train Epoch: 39 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.007008
Train Epoch: 40 [0/60000 (0%)]	Loss: nan
Train Epoch: 40 [25600/60000 (43%)]	Loss: nan
Train Epoch: 40 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006856
Train Epoch: 41 [0/60000 (0%)]	Loss: nan
Train Epoch: 41 [25600/60000 (43%)]	Loss: nan
Train Epoch: 41 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006701
Train Epoch: 42 [0/60000 (0%)]	Loss: nan
Train Epoch: 42 [25600/60000 (43%)]	Loss: nan
Train Epoch: 42 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006545
Train Epoch: 43 [0/60000 (0%)]	Loss: nan
Train Epoch: 43 [25600/60000 (43%)]	Loss: nan
Train Epoch: 43 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006387
Train Epoch: 44 [0/60000 (0%)]	Loss: nan
Train Epoch: 44 [25600/60000 (43%)]	Loss: nan
Train Epoch: 44 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006227
Train Epoch: 45 [0/60000 (0%)]	Loss: nan
Train Epoch: 45 [25600/60000 (43%)]	Loss: nan
Train Epoch: 45 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.006066
Train Epoch: 46 [0/60000 (0%)]	Loss: nan
Train Epoch: 46 [25600/60000 (43%)]	Loss: nan
Train Epoch: 46 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005904
Train Epoch: 47 [0/60000 (0%)]	Loss: nan
Train Epoch: 47 [25600/60000 (43%)]	Loss: nan
Train Epoch: 47 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005741
Train Epoch: 48 [0/60000 (0%)]	Loss: nan
Train Epoch: 48 [25600/60000 (43%)]	Loss: nan
Train Epoch: 48 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005577
Train Epoch: 49 [0/60000 (0%)]	Loss: nan
Train Epoch: 49 [25600/60000 (43%)]	Loss: nan
Train Epoch: 49 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005413
Train Epoch: 50 [0/60000 (0%)]	Loss: nan
Train Epoch: 50 [25600/60000 (43%)]	Loss: nan
Train Epoch: 50 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005248
Train Epoch: 51 [0/60000 (0%)]	Loss: nan
Train Epoch: 51 [25600/60000 (43%)]	Loss: nan
Train Epoch: 51 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.005083
Train Epoch: 52 [0/60000 (0%)]	Loss: nan
Train Epoch: 52 [25600/60000 (43%)]	Loss: nan
Train Epoch: 52 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004917
Train Epoch: 53 [0/60000 (0%)]	Loss: nan
Train Epoch: 53 [25600/60000 (43%)]	Loss: nan
Train Epoch: 53 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004752
Train Epoch: 54 [0/60000 (0%)]	Loss: nan
Train Epoch: 54 [25600/60000 (43%)]	Loss: nan
Train Epoch: 54 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004587
Train Epoch: 55 [0/60000 (0%)]	Loss: nan
Train Epoch: 55 [25600/60000 (43%)]	Loss: nan
Train Epoch: 55 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004423
Train Epoch: 56 [0/60000 (0%)]	Loss: nan
Train Epoch: 56 [25600/60000 (43%)]	Loss: nan
Train Epoch: 56 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004259
Train Epoch: 57 [0/60000 (0%)]	Loss: nan
Train Epoch: 57 [25600/60000 (43%)]	Loss: nan
Train Epoch: 57 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.004096
Train Epoch: 58 [0/60000 (0%)]	Loss: nan
Train Epoch: 58 [25600/60000 (43%)]	Loss: nan
Train Epoch: 58 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003934
Train Epoch: 59 [0/60000 (0%)]	Loss: nan
Train Epoch: 59 [25600/60000 (43%)]	Loss: nan
Train Epoch: 59 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003773
Train Epoch: 60 [0/60000 (0%)]	Loss: nan
Train Epoch: 60 [25600/60000 (43%)]	Loss: nan
Train Epoch: 60 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003613
Train Epoch: 61 [0/60000 (0%)]	Loss: nan
Train Epoch: 61 [25600/60000 (43%)]	Loss: nan
Train Epoch: 61 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003455
Train Epoch: 62 [0/60000 (0%)]	Loss: nan
Train Epoch: 62 [25600/60000 (43%)]	Loss: nan
Train Epoch: 62 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003299
Train Epoch: 63 [0/60000 (0%)]	Loss: nan
Train Epoch: 63 [25600/60000 (43%)]	Loss: nan
Train Epoch: 63 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.003144
Train Epoch: 64 [0/60000 (0%)]	Loss: nan
Train Epoch: 64 [25600/60000 (43%)]	Loss: nan
Train Epoch: 64 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002992
Train Epoch: 65 [0/60000 (0%)]	Loss: nan
Train Epoch: 65 [25600/60000 (43%)]	Loss: nan
Train Epoch: 65 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002841
Train Epoch: 66 [0/60000 (0%)]	Loss: nan
Train Epoch: 66 [25600/60000 (43%)]	Loss: nan
Train Epoch: 66 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002693
Train Epoch: 67 [0/60000 (0%)]	Loss: nan
Train Epoch: 67 [25600/60000 (43%)]	Loss: nan
Train Epoch: 67 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002548
Train Epoch: 68 [0/60000 (0%)]	Loss: nan
Train Epoch: 68 [25600/60000 (43%)]	Loss: nan
Train Epoch: 68 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002405
Train Epoch: 69 [0/60000 (0%)]	Loss: nan
Train Epoch: 69 [25600/60000 (43%)]	Loss: nan
Train Epoch: 69 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002265
Train Epoch: 70 [0/60000 (0%)]	Loss: nan
Train Epoch: 70 [25600/60000 (43%)]	Loss: nan
Train Epoch: 70 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.002128
Train Epoch: 71 [0/60000 (0%)]	Loss: nan
Train Epoch: 71 [25600/60000 (43%)]	Loss: nan
Train Epoch: 71 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.001995
Train Epoch: 72 [0/60000 (0%)]	Loss: nan
Train Epoch: 72 [25600/60000 (43%)]	Loss: nan
Train Epoch: 72 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.001864
Train Epoch: 73 [0/60000 (0%)]	Loss: nan
Train Epoch: 73 [25600/60000 (43%)]	Loss: nan
Train Epoch: 73 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.001737
Train Epoch: 74 [0/60000 (0%)]	Loss: nan
Train Epoch: 74 [25600/60000 (43%)]	Loss: nan
Train Epoch: 74 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.001614
Train Epoch: 75 [0/60000 (0%)]	Loss: nan
Train Epoch: 75 [25600/60000 (43%)]	Loss: nan
Train Epoch: 75 [51200/60000 (85%)]	Loss: nan
================================================================
Training: Average loss: nan, Accuracy: 6000/60000 (10%)
Test: Average loss: nan, Accuracy: 1000/10000 (10%)
================================================================
当前学习率: 0.001494
