====================================================
                  TRADES 训练配置                    
====================================================
日期时间: Tue 22 Jul 2025 08:44:06 AM UTC
数据集: cifar10
模型: densenet121
批大小: 128
学习率: 0.01
动量: 0.9
权重衰减: 5e-4
epsilon: 0.031
步长: 0.007843137
扰动步数: 20
正则化系数beta: 6.0
训练轮数: 100
保存频率: 10
混合精度训练: 开启
训练脚本: train_trades_cifar10_3.py
====================================================

Files already downloaded and verified
Files already downloaded and verified
Using automatic mixed precision training
模型将保存到: ./model-cifar10-resnet50/cifar10_densenet121_bs128_lr0.01
train_trades_cifar10_3.py:83: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler() if args.amp else None
train_trades_cifar10_3.py:92: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/_reduction.py:42: UserWarning: size_average and reduce args will be deprecated, please use reduction='sum' instead.
  warnings.warn(warning.format(ret))
Traceback (most recent call last):
  File "train_trades_cifar10_3.py", line 244, in <module>
    main() 
  File "train_trades_cifar10_3.py", line 227, in main
    train(args, model, device, train_loader, optimizer, epoch)
  File "train_trades_cifar10_3.py", line 94, in train
    loss = trades_loss(model=model,
  File "/home/<USER>/trades_project/TRADES/trades.py", line 37, in trades_loss
    F.softmax(model(x_natural), dim=1))
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/trades_project/TRADES/models/densenet.py", line 87, in forward
    out = self.trans3(self.dense3(out))
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/container.py", line 219, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/trades_project/TRADES/models/densenet.py", line 15, in forward
    out = self.conv1(F.relu(self.bn1(x)))
  File "/home/<USER>/anaconda3/envs/trades_env2/lib/python3.8/site-packages/torch/nn/functional.py", line 1500, in relu
    result = torch.relu(input)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 44.35 GiB of which 19.56 MiB is free. Process 1155147 has 6.97 GiB memory in use. Process 648654 has 7.25 GiB memory in use. Process 1689622 has 2.39 GiB memory in use. Process 1895234 has 12.83 GiB memory in use. Process 2012405 has 4.16 GiB memory in use. Process 3145237 has 5.98 GiB memory in use. Including non-PyTorch memory, this process has 4.71 GiB memory in use. Of the allocated memory 4.29 GiB is allocated by PyTorch, and 106.18 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
